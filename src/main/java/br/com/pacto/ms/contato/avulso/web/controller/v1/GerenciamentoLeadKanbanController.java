package br.com.pacto.ms.contato.avulso.web.controller.v1;

import br.com.pacto.ms.contato.avulso.service.contract.GerenciamentoLeadKanbanService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.KANBAN;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.KANBAN_DESCRICAO;

@Tag(name = <PERSON>AN<PERSON><PERSON>, description = KANBAN_DESCRICAO)
@RestController
@RequestMapping("/v1/lead/kanban")
public class GerenciamentoLeadKanbanController extends BaseController {

    private GerenciamentoLeadKanbanService gerenciamentoLeadKanbanService;

    public GerenciamentoLeadKanbanController(GerenciamentoLeadKanbanService gerenciamentoLeadKanbanService) {
        this.gerenciamentoLeadKanbanService = gerenciamentoLeadKanbanService;
    }

    @Operation(summary = "Realiza uma requisição POST para o servlet do ZW (Classe: GerenciamentoLeadsServletControle) enviando um corpo JSON com a operação desejada e parâmetros relacionados.")
    @PostMapping("/realizarOperacao")
    @LogExecution
    public ResponseEntity<?> realizarOperacao(@RequestBody Map<String, Object> body, HttpServletRequest request) {
        try {
            JSONObject corpoJson = new JSONObject(body);
            JSONObject resposta = gerenciamentoLeadKanbanService.realizarOperacao(corpoJson);
            return ResponseEntity.ok(resposta.toMap());
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Erro ao realizar operação " + body.get("operacao") + ": " + e.getMessage());
        }
    }

    @Operation(summary = "Realiza uma requisição POST para o servlet do ZW (Classe: GerenciamentoLeadsServletControle) enviando um corpo JSON com a operação desejada e parâmetros relacionados.")
    @GetMapping("/listarTags")
    @LogExecution
    public ResponseEntity<?> realizarOperacaoListarTags(
            HttpServletRequest request,
            @RequestParam(value = "filters") String filtersJson,
            @RequestParam(value = "configs", required = false) String configsJson,
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "size", required = false) Integer size,
            @RequestParam(value = "sort", required = false) String sort) {
        try {
            JSONObject corpoJson = new JSONObject();
            JSONObject filtersJsonObject = new JSONObject(filtersJson);
            corpoJson.put("operacao", "LISTA_TAGS");
            corpoJson.put("filters", filtersJsonObject);
            corpoJson.put("sort", sort);
            corpoJson.put("page", page);
            corpoJson.put("size", size);
            corpoJson.put("configs", configsJson);
            JSONObject resposta = gerenciamentoLeadKanbanService.realizarOperacao(corpoJson);
            return ResponseEntity.ok(resposta.toMap());
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Erro ao realizar operação LISTA_TAGS: " + e.getMessage());
        }
    }

    @Operation(summary = "Realiza uma requisição POST para o servlet do ZW (Classe: GerenciamentoLeadsServletControle) enviando um corpo JSON com a operação desejada e parâmetros relacionados.")
    @GetMapping("/listarFunis")
    @LogExecution
    public ResponseEntity<?> realizarOperacaoListarFunis(
            HttpServletRequest request,
            @RequestParam(value = "filters") String filtersJson,
            @RequestParam(value = "configs", required = false) String configsJson,
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "size", required = false) Integer size,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestParam(value = "companyId", required = false) Integer empresa ) {
        try {
            JSONObject corpoJson = new JSONObject();
            JSONObject filtersJsonObject = new JSONObject(filtersJson);
            corpoJson.put("operacao", "CONSULTAR_LISTA_FUNIS");
            corpoJson.put("filters", filtersJsonObject);
            corpoJson.put("sort", sort);
            corpoJson.put("page", page);
            corpoJson.put("size", size);
            corpoJson.put("configs", configsJson);
            corpoJson.put("companyId", empresa);
            JSONObject resposta = gerenciamentoLeadKanbanService.realizarOperacao(corpoJson);
            return ResponseEntity.ok(resposta.toMap());
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Erro ao realizar operação CONSULTAR_LISTA_FUNIS: " + e.getMessage());
        }
    }
}