package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAEntity;
import lombok.Data;

@Data
public class ControleFeaturesVO {

    private Boolean desabilitarAulaExperimental;
    private Boolean desabilitarPlanos;
    private Boolean desabilitarProdutos;
    private Boolean desabilitarTurmas;
    private Boolean desabilitarLigacao;
    private Boolean desabilitarVisita;

    public static ControleFeaturesVO toVo(ConfiguracaoCrmIAEntity entity) {
        ControleFeaturesVO controleFeaturesVO = new ControleFeaturesVO();
        controleFeaturesVO.setDesabilitarAulaExperimental(entity.getDesabilitarAgendamentoAulasExperimentais());
        controleFeaturesVO.setDesabilitarPlanos(entity.getDesabilitarPlanos());
        controleFeaturesVO.setDesabilitarTurmas(entity.getDesabilitarTurmas());
        controleFeaturesVO.setDesabilitarProdutos(entity.getDesabilitarProdutos());
        controleFeaturesVO.setDesabilitarLigacao(entity.getDesabilitarLigacao());
        controleFeaturesVO.setDesabilitarVisita(entity.getDesabilitarVisita());
        return controleFeaturesVO;
    }
}
