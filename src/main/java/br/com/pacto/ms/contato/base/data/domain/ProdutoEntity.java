package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "produto", schema = "public")
public class ProdutoEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Column(name = "desativado")
    private Boolean desativado;

    @Column(name = "tipoproduto", length = 2)
    private String tipoproduto;

    @Column(name = "valorbasecalculo")
    private Float valorbasecalculo;

    @Column(name = "valorfinal")
    private Float valorfinal;

    @Column(name = "nrdiasvigencia")
    private Integer nrdiasvigencia;

    @Column(name = "datafinalvigenciafixa")
    private Date datafinalvigenciafixa;

    @Column(name = "datainiciovigencia")
    private Date datainiciovigencia;

    @Column(name = "tipovigencia", length = 2)
    private String tipovigencia;

    @Column(name = "descricao", nullable = false, length = 50)
    private String descricao;

    @Column(name = "apresentarvendasonline", columnDefinition = "boolean default false")
    private Boolean apresentarvendasonline;

    @Column(name = "habilitarIa", columnDefinition = "boolean default false")
    private Boolean habilitarIa;

}
