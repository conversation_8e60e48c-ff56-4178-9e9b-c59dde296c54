package br.com.pacto.ms.contato.base.data.domain;


import lombok.Data;

import javax.persistence.*;
import java.sql.Timestamp;

@Data
@Entity
@Table(name = "turma")
public class TurmaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    private Boolean bloquearmatriculasacimalimite;

    private Integer empresa;

    private Integer idademaxima;
    private Integer idademaximameses = 0;
    private Integer idademinima;
    private Integer idademinimameses = 0;
    private Timestamp datafinalvigencia;
    private Timestamp datainicialvigencia;

    @ManyToOne
    @JoinColumn(name = "modalidade", foreignKey = @ForeignKey(name = "fk_turma_modalidade"))
    private ModalidadeEntity modalidade;

    private String identificador;
    private String descricao;
    private Short minutosantecedenciamarcaraula = 0;
    private Short minutosantecedenciadesmarcaraula = 0;
    private Short tipoantecedenciamarcaraula = 1;
    private Integer usuariodesativou;
    private Boolean aulacoletiva = false;
    private Double meta;
    private Double pontosbonus;
    private Double bonificacao;
    private Integer ocupacao;
    private Integer codigoaulacheia;
    private String mensagem;
    private String dias;
    private String horarios;
    private Integer professor;
    private Integer tolerancia;
    private Integer capacidade;
    private Integer ambiente;
    private Boolean bloquearreposicaoacimalimite = false;
    private Boolean permitiraulaexperimental = false;
    private Boolean permitirdesmarcarreposicoes = true;
    private Boolean validarrestricoesmarcacao = true;
    private Integer qtdenivelocupacao;
    private Double percdescocupacaonivel1;
    private Double percdescocupacaonivel2;
    private Double percdescocupacaonivel3;
    private Double percdescocupacaonivel4;
    private Double percdescocupacaonivel5;
    private Integer minutosaposinicioapp;
    private Boolean integracaospivi;
    private Boolean permitealunooutraempresa = false;
    private Integer produtogympass;
    private Integer idclassegympass;
    private String urlturmavirtual;
    private Boolean naovalidarmodalidadecontrato = false;
    private Integer tipotolerancia = 1;
    private String urlvideoyoutube;
    private String fotokey;
    private Boolean visualizarprodutosgympass = true;
    private Boolean visualizarprodutostotalpass = true;
    private String cor;
    private Boolean permitefixar = false;
    private String idexterno;
    private Boolean bloquearlotacaofutura = false;
    private String niveis;
    private Boolean habilitarIa;
}
