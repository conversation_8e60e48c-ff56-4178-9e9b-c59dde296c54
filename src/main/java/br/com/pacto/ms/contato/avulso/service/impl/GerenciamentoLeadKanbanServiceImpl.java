package br.com.pacto.ms.contato.avulso.service.impl;

import br.com.pacto.ms.contato.avulso.service.contract.GerenciamentoLeadKanbanService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaosistemacrmEntity;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.hibernate.service.spi.ServiceException;

import java.util.Collections;

@Slf4j
@Service
public class GerenciamentoLeadKanbanServiceImpl implements GerenciamentoLeadKanbanService {

    private static final String PATH_SERVLET = "/prest/gerenciamentoleads";

    private RequestService requestService;

    public GerenciamentoLeadKanbanServiceImpl(RequestService requestService) {
        this.requestService = requestService;
    }

    @Override
    public JSONObject realizarOperacao(JSONObject corpoRequisicao) throws ServiceException {

        try {
            String chave = requestService.getCurrentConfiguration().getCompanyKey();

            Integer empresa = requestService.getCurrentConfiguration().getCompanyId();

            if(corpoRequisicao.has("companyId")){
                empresa = corpoRequisicao.getInt("companyId");
            }

            String username = requestService.getCurrentConfiguration().getUsername();
            String urlZW = requestService.getClienteDiscovery().getServiceUrls().getZwUrl();

            String urlZw = urlZW + PATH_SERVLET + "?key=" + chave + "&empresa=" + empresa + "&username=" + username;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

            HttpEntity<String> body = new HttpEntity<>(corpoRequisicao.toString(), headers);

            // Log do curl equivalente
            String curl = "curl -X POST \"" + urlZw + "\" \\\n"
                    + "  -H \"Content-Type: application/json\" \\\n"
                    + "  -H \"Accept: application/json\" \\\n"
                    + "  -d '" + corpoRequisicao.toString().replace("'", "\\'") + "'";
            log.info("Requisição ZW - CURL equivalente:\n" + curl);

            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(
                    urlZw,
                    HttpMethod.POST,
                    body,
                    String.class
            );
            
            return new JSONObject(response.getBody());

        } catch (Exception e) {
            throw new ServiceException("Erro ao realizar operacao '" + corpoRequisicao.optString("operacao") + "': " + e.getMessage(), e);
        }

    }


}
