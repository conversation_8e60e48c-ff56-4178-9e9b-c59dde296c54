package br.com.pacto.ms.contato.ia.worker;

import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.HistoricoPromptService;
import br.com.pacto.ms.contato.ia.data.pojo.input.ApiProxyDTO;
import br.com.pacto.ms.contato.ia.data.pojo.input.AutenticacaoDTO;
import br.com.pacto.ms.contato.ia.data.pojo.input.PersonaDTO;
import br.com.pacto.ms.contato.ia.data.proxy.proxy.ApiProxy;
import br.com.pacto.ms.oamd.domain.OAMDConfiguracaoHorarioPadraoConversasIA;
import br.com.pacto.ms.oamd.domain.OAMDLogConversasIA;
import br.com.pacto.ms.oamd.repository.OAMDConfiguracaoHorarioPadraoConversasIARepository;
import br.com.pacto.ms.oamd.repository.OAMDLogConversasIARepository;
import br.com.pactosolucoes.commons.data.domain.OAMDCompany;

import br.com.pactosolucoes.commons.data.dto.discovery.ClientDiscoveryDataDTO;
import br.com.pactosolucoes.commons.data.repository.OAMDCompanyRepository;

import br.com.pactosolucoes.commons.web.security.contract.UrlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class IAScheduler {

    @Value("${autenticacao.id}")
    String autenticacaoId;
    @Autowired
    OAMDCompanyRepository companyRepository;
    @Autowired
    private UrlService urlService;
    @Autowired
    private ApiProxy apiProxy;
    @Autowired
    private OAMDConfiguracaoHorarioPadraoConversasIARepository oamdConfiguracaoHorarioPadraoConversasIARepository;
    @Autowired
    private OAMDLogConversasIARepository oamdLogConversasIARepository;
    @Autowired
    private HistoricoPromptService historicoPromptService;

    private static final int THREAD_POOL_SIZE = 2;

    @Value("${thread.pool.size}")
    private int threadPoolSize;

    private static final Logger log = LoggerFactory.getLogger(IAScheduler.class);

    @Scheduled(cron = "0 */30 * * * ?")
    public void inciarConversas() {
        Instant startTime = Instant.now();
        log.debug("Início do processamento de envio de mensagens Orion");

        Optional<OAMDConfiguracaoHorarioPadraoConversasIA> oamdConfiguracaoHorarioPadraoConversasIA = oamdConfiguracaoHorarioPadraoConversasIARepository.buscarPorHora(LocalTime.now().withMinute(0).withSecond(0));

        List<String> listaDeEmpresasAntigas = oamdConfiguracaoHorarioPadraoConversasIA.map(OAMDConfiguracaoHorarioPadraoConversasIA::getChaves).orElse(Collections.emptyList());

        List<OAMDCompany> empresas = this.companyRepository.findAllById(listaDeEmpresasAntigas);

        ExecutorService executorService;

        if (threadPoolSize > 0)
            executorService = Executors.newFixedThreadPool(threadPoolSize);
        else
            executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);

        try {
            List<Callable<Void>> tasks = empresas.stream().map(empresa -> (Callable<Void>) () -> {
                Instant empresaStartTime = Instant.now();
                salvarLogConversas(empresa, "Processamento iniciado para a empresa: " + empresa.getChave());

                try {
                    processarEmpresa(empresa);
                    Instant empresaEndTime = Instant.now();
                    salvarLogConversas(empresa, "Processamento concluído para a empresa: " + empresa.getChave() +
                            ". Tempo de processamento: " + Duration.between(empresaStartTime, empresaEndTime).toMillis() + " ms");
                } catch (Exception e) {
                    salvarLogConversas(empresa, "Erro ao processar a empresa: " + empresa.getChave() + ". Mensagem: " + e.getMessage());
                    log.warn("Erro ao processar empresa em uma das threads", e);
                }

                return null;
            }).collect(Collectors.toList());


            List<Future<Void>> results = executorService.invokeAll(tasks);

            for (Future<Void> result : results) {
                try {
                    result.get();
                } catch (Exception e) {
                    log.warn("Erro ao processar empresa em uma das threads", e);
                }
            }
        } catch (InterruptedException e) {
            log.error("O processamento foi interrompido", e);
            Thread.currentThread().interrupt();
        } finally {
            executorService.shutdown();
        }

        Instant endTime = Instant.now();
        Duration tempoProcessamentoTotal = Duration.between(startTime, endTime);
        log.debug("Fim do processamento Orion. Tempo total de processamento: {} ms", tempoProcessamentoTotal.toMillis());
    }

    private OAMDLogConversasIA salvarLogConversas(OAMDCompany empresa, String log) {
        return oamdLogConversasIARepository.save(OAMDLogConversasIA.builder()
                .chaveEmpresa(empresa.getChave())
                .dia(Instant.now().toString())
                .hora(LocalTime.now())
                .log(log)
                .build());
    }

    private void processarEmpresa(OAMDCompany empresa) {
        Instant startTimeEmpresa = Instant.now();

        if (!empresa.getModulos().contains("ZW") || !empresa.getModulos().contains("CRM") || !empresa.getModulos().contains("IA")) {
            log.warn(empresa.getNomeBD() + " - A empresa: " + empresa.getChave() + " não possui os módulos necessários para o processamento. Pulando para a próxima empresa.");
            return;
        }

        ClientDiscoveryDataDTO clientDiscoveryDataDTO = urlService.getClienteDiscovery(empresa.getChave());
        log.debug(empresa.getNomeBD() + " - Início do processamento da empresa " + empresa.getIdentificadorempresa());

        try {
            AutenticacaoDTO autenticacaoMsContentResponseVO = getAutenticacaoMsV2ContentResponseVO(empresa, clientDiscoveryDataDTO.getServiceUrls().getAutenticacaoUrl());
            this.apiProxy.mandarPost(clientDiscoveryDataDTO.getServiceUrls().getContatoMsUrl() + "/v1/ia/conversa/fase/crm/" + empresa.getChave()+"?forcarEnvio=false", autenticacaoMsContentResponseVO.getContent(), null, ApiProxyDTO.class);
        } catch (Exception e) {
            log.warn(empresa.getNomeBD() + " - Não foi possível processar a empresa: " + empresa.getChave() + ".", e);
        }


        Instant endTimeEmpresa = Instant.now();
        Duration tempoProcessamentoEmpresa = Duration.between(startTimeEmpresa, endTimeEmpresa);
        log.debug(empresa.getNomeBD() + " - Fim do processamento da empresa " + empresa.getIdentificadorempresa() + ". Tempo de processamento: {} ms", tempoProcessamentoEmpresa.toMillis());
    }

    private AutenticacaoDTO getAutenticacaoMsV2ContentResponseVO(OAMDCompany company, String baseUrl) {

        return this.apiProxy.mandarPost(baseUrl + "/aut/v2/gt", null, PersonaDTO
                .builder()
                .chave(company.getChave())
                .id(autenticacaoId)
                .build(), AutenticacaoDTO.class);
    }


    @Scheduled(cron = "0 0 0 * * 0")
    public void executarLimpezaAutomatica() {
        log.info("Iniciando limpeza automática do histórico de prompts");

        try {
            int registrosRemovidos = historicoPromptService.removerHistoricoAntigo();
            if (registrosRemovidos > 0) {
                log.info("Limpeza automática concluída. {} registros removidos do histórico de prompts",
                        registrosRemovidos);
            } else {
                log.debug("Limpeza automática concluída. Nenhum registro antigo encontrado para remoção");
            }
        } catch (Exception e) {
            log.error("Erro durante a limpeza automática do histórico de prompts", e);
        }
    }

}
