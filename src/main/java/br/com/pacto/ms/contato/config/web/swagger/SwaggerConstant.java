package br.com.pacto.ms.contato.config.web.swagger;

public interface SwaggerConstant {

    String HISTORICO_CONTATO_PESSOAL = "Histórico Contato Pessoal";
    String HISTORICO_CONTATO_PESSOAL_DESCRICAO = "Operações de gestão do histórico de contatos pessoais (avulsos) realizados.";

    String COLABORADOR = "Colaborador";
    String COLABORADOR_DESCRICAO = "Operações de gestão de colaboradores.";

    String CLIENTES = "Clientes";
    String CLIENTES_DESCRICAO = "Operações de gestão de clientes.";

    String MODALIDADE = "Modalidade";
    String MODALIDADE_DESCRICAO = "Operações de gestão de modalidades";

    String OBJECAO = "Objeção";
    String OBJECAO_DESCRICAO = "Grupo de Objeção.";

    String QUESTIONARIO = "Questionário";
    String QUESTIONARIO_DESCRICAO = "Operações de gestão de questionários.";

    String CONFIGURACAO_COMUM = "Configurações Comum";
    String CONFIGURACAO_COMUM_DESCRICAO = "Configurações comun do sistema, ou seja, serve para todos os sistemas";

    String CONFIGURACAO_CRM = "Configurações CRM";
    String CONFIGURACAO_CRM_DESCRICAO = "Configuração relacionadas ao CRM";

    String CONFIGURACAO_EMPRESA = "Configurações da Empresa";
    String CONFIGURACAO_COMUM_EMPRESA = "Configuração empresa, ou seja, serve para todas as configruações relacionadas a empresa";

    String RECEPTIVO = "Receptivo";
    String RECEPTIVO_DESCRICAO = "Operaçoes voltadas a Receptivos";

	String LEAD = "Lead";
	String LEAD_DESCRICAO = "Operaçoes de Lead ";

	String KANBAN = "Gerenciamento Lead Kanban";
	String KANBAN_DESCRICAO = "Opera sobre funis e cards de leads no quadro kanban do CRM";

	String CONFIGURACAO_FUNIL = "Configuracao do Funil";
	String CONFIGURACAO_FUNIL_DESCRICAO = "Configuração relacionadas ao Funil";
}
