package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracaosistemacrmVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Boolean abertodomingo;

	private Boolean abertosabado;

	private Boolean agendamentoparametaconsultor;

	private Boolean apresentarcolaboradoresinativos;

	private Boolean apresentarcolaboradoresportipocolaborador;

	private Boolean autorrenovavelentrarenovacao;

	private Boolean batermetatodasacoes;

	private Boolean bloqueartermospam;

	private Boolean conexaosegura;

	private Boolean considerarprofessortreinoweb;

	private Boolean dividirfase;

	private String emailpadrao;

	private Boolean enviaremailindividualmente;

	private Boolean incluircontratosrenovados;

	private Boolean iniciartls;

	private Boolean integracaopacto;

	private Boolean integracaosendyativa;

	private Integer limitediarioemails;

	private Integer limitemensalpacto;

	private String login;

	private String mailingftpfolder;

	private String mailingftppass;

	private Integer mailingftpport;

	private String mailingftpserver;

	private String mailingftptype;

	private String mailingftpuser;

	private String mailserver;

	private Integer nrcreditostreinorenovar;

	private Integer nrdiasanterioresagendamento;

	private Integer nrdiascontarresultado;

	private Integer nrdiaslimiteagendamentofuturo;

	private Integer nrdiasparaclientepreveperda;

	private Integer nrdiasparaclientepreverenovacao;

	private Integer nrdiasparaclientepreverenovacaomaiorummes;

	private Integer nrdiasposagendamentoconversaoexaluno;

	private Integer nrdiasposterioresagendamento;

	private Integer nrfaltaplanoacimasemestral;

	private Integer nrfaltaplanomensal;

	private Integer nrfaltaplanotrimestral;

	private Integer nrrisco;

	private Boolean obrigatorioseguirordemmetas;

	private String ordenacaometas;

	private String portaserver;

	private Integer qtdconversoesexalunosmes;

	private Integer qtdconversoesvendasmes;

	private Integer qtdindicacoesmes;

	private String remetentepadrao;

	private Integer remetentepadraomailing;

	private String senha;

	private String tokenbitly;

	private String urljenkins;

	private String urlmailing;

	private Boolean usarremetentepadraogeral;

	private Boolean usasmtps;

}
