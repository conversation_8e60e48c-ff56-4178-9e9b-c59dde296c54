package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.avulso.service.contract.GerenciamentoLeadKanbanService;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.CONFIGURACAO_FUNIL;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.CONFIGURACAO_FUNIL_DESCRICAO;

@Slf4j
@Validated
@Tag(name = CONFIGURACAO_FUNIL, description = CONFIGURACAO_FUNIL_DESCRICAO)

@RestController
@Tag(name = "Funil", description = "Endpoint para dar suporte ao exportar do funil")
@RequestMapping("/funil-vendas")
public class FunilCRMController extends BaseController {

    @Autowired
    private GerenciamentoLeadKanbanService gerenciamentoLeadKanbanService;

    @GetMapping("/compartilhar")
    public ResponseEntity<?> compartilharDados(
            @RequestParam(value = "filters") String filtersJson,
            @RequestParam(value = "colunas", required = false) String colunasJson,
            @RequestParam(value = "empresa", defaultValue = "0") int empresa,
            @RequestParam(value = "pagina", defaultValue = "0") int pagina
    ) {
        try {
            JSONObject filtros = new JSONObject(filtersJson);
            filtros.put("operacao","RELATORIO_FUNIL");
            JSONObject retorno = gerenciamentoLeadKanbanService.realizarOperacao(filtros);

            // Adapta o formato para o getData funcionar
            JSONObject response = new JSONObject();
            response.put("content", retorno.getJSONArray("dados"));

            return ResponseEntity.ok(response.toString());

        } catch (Exception e) {
            log.error("Erro ao gerar dados do relatório de funil", e);
            Map<String, Object> erro = new HashMap<>();
            erro.put("erro", "Erro ao gerar dados do relatório: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(erro);
        }
    }

}
