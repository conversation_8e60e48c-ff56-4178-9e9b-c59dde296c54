package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;

import br.com.pacto.ms.comuns.data.pojo.output.ProdutoVO;
import br.com.pacto.ms.contato.base.data.domain.ProdutoEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface ProdutoRepository extends PagingAndSortingRepository<ProdutoEntity, Integer> {

    @Query("SELECT new br.com.pacto.ms.comuns.data.pojo.output.ProdutoVO(p.codigo, p.valorfinal, p.descricao) " +
            " FROM ProdutoEntity p WHERE p.desativado = false " +
            " and p.apresentarvendasonline = true " +
            " and p.habilitarIa = true" )
    Optional<List<ProdutoVO>> getAllProdutosVendasOnline();
}

